"""Demo script for the multi-agent system."""

import asyncio
from domain_agents.orchestrator import MultiAgentOrchestrator


async def demo_agent_routing():
    """Demonstrate agent routing and classification."""
    print("🎯 Multi-Agent System Demo")
    print("=" * 50)
    
    orchestrator = MultiAgentOrchestrator()
    
    # Demo tasks for each agent
    demo_tasks = [
        {
            "task": "What are the key HIPAA compliance requirements for patient data storage?",
            "description": "Healthcare compliance question"
        },
        {
            "task": "Explain the differences between BERT and GPT transformer architectures",
            "description": "AI research technical question"
        },
        {
            "task": "Create a 6-month project plan for implementing a new CRM system",
            "description": "Administrative project management task"
        },
        {
            "task": "How can machine learning be applied to improve patient diagnosis accuracy?",
            "description": "Cross-domain question (AI + Healthcare)"
        },
        {
            "task": "Draft a compliance report for our AI model deployment in healthcare",
            "description": "Cross-domain question (Admin + Healthcare + AI)"
        }
    ]
    
    print("\n🧪 Testing Agent Classification and Routing")
    print("-" * 50)
    
    for i, demo in enumerate(demo_tasks, 1):
        print(f"\n{i}. {demo['description']}")
        print(f"Task: {demo['task'][:80]}...")
        
        # Classify the task
        domain = orchestrator.classify_task(demo['task'])
        agent = orchestrator.get_agent_by_domain(domain)
        
        print(f"🎯 Routed to: {agent.name} ({domain} domain)")
        
        # Show agent capabilities
        if domain == "healthcare":
            print("   💊 Capabilities: Medical compliance, clinical practices, healthcare regulations")
        elif domain == "ai_research":
            print("   🤖 Capabilities: ML algorithms, model architectures, AI research")
        elif domain == "admin":
            print("   📋 Capabilities: Project management, documentation, workflow optimization")
    
    print("\n" + "=" * 50)
    print("✅ Agent routing demonstration complete!")
    print("\nKey Features Demonstrated:")
    print("• Intelligent task classification")
    print("• Domain-specific agent routing")
    print("• Specialized agent capabilities")
    print("• Cross-domain task handling")


async def demo_memory_operations():
    """Demonstrate memory operations (simulated)."""
    print("\n🧠 Memory Operations Demo (Simulated)")
    print("=" * 50)
    
    print("Note: This demonstrates how memory operations would work")
    print("when the Mem0 MCP server is running.\n")
    
    # Simulated memory operations
    operations = [
        {
            "operation": "add_memory",
            "description": "Adding new memory about healthcare compliance",
            "data": "HIPAA requires encryption of patient data at rest and in transit"
        },
        {
            "operation": "retrieve_memory", 
            "description": "Searching for healthcare compliance memories",
            "query": "HIPAA encryption requirements"
        },
        {
            "operation": "store_memory",
            "description": "Updating existing memory with new information",
            "data": "Updated HIPAA guidelines include new cloud storage requirements"
        },
        {
            "operation": "delete_memory",
            "description": "Removing outdated compliance information",
            "memory_id": "old_compliance_001"
        }
    ]
    
    for i, op in enumerate(operations, 1):
        print(f"{i}. {op['operation'].upper()}")
        print(f"   Description: {op['description']}")
        if 'data' in op:
            print(f"   Data: {op['data']}")
        if 'query' in op:
            print(f"   Query: {op['query']}")
        if 'memory_id' in op:
            print(f"   Memory ID: {op['memory_id']}")
        print("   Status: ✅ Would execute when MCP server is available\n")
    
    print("Memory operations support:")
    print("• Persistent context across agent interactions")
    print("• Semantic search and retrieval")
    print("• Memory updates and management")
    print("• Integration with agent workflows")


async def main():
    """Run the complete demo."""
    print("🚀 Multi-Agent System with Mem0 MCP Integration")
    print("🎬 DEMONSTRATION MODE")
    print("=" * 60)
    
    await demo_agent_routing()
    await demo_memory_operations()
    
    print("\n" + "=" * 60)
    print("🎉 Demo Complete!")
    print("\nTo run the actual system:")
    print("• python main.py agents    - Test agent routing")
    print("• python main.py memory    - Test memory operations (requires MCP server)")
    print("• python main.py           - Run all tests")
    print("\nFor interactive mode:")
    print("• python main.py interactive")


if __name__ == "__main__":
    asyncio.run(main())
