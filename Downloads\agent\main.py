"""Main entry point for the multi-agent system."""

import asyncio
import sys
from typing import Optional

from domain_agents.orchestrator import MultiAgentOrchestrator
from mcp.mem0_client import Mem0<PERSON><PERSON>lient
from mcp.memory_operations import MemoryOperationsTester
from config.settings import settings


async def test_memory_operations():
    """Test Mem0 memory operations."""
    print("🧠 Testing Mem0 Memory Operations")
    print("=" * 50)
    
    async with Mem0MCPClient() as client:
        if not await client.is_connected():
            print("❌ Failed to connect to Mem0 MCP server")
            print(f"Server URL: {settings.mem0_mcp_url}")
            print("Please ensure the Mem0 MCP server is running")
            return False
        
        # List available tools
        print("\n📋 Available MCP Tools:")
        tools = await client.list_available_tools()
        for tool in tools:
            print(f"  - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")
        
        # Run memory operation tests
        tester = MemoryOperationsTester(client)
        summary = await tester.run_all_tests()
        
        return summary["success_rate"] > 0.5  # Consider successful if >50% tests pass


async def test_agents():
    """Test the multi-agent system."""
    print("\n🤖 Testing Multi-Agent System")
    print("=" * 50)
    
    orchestrator = MultiAgentOrchestrator()
    
    # Test tasks for each agent
    test_tasks = [
        {
            "task": "Explain the importance of HIPAA compliance in healthcare data management",
            "expected_domain": "healthcare"
        },
        {
            "task": "Compare transformer architectures with CNN models for image classification",
            "expected_domain": "ai_research"
        },
        {
            "task": "Create a project timeline for implementing a new software system",
            "expected_domain": "admin"
        }
    ]
    
    for i, test_case in enumerate(test_tasks, 1):
        print(f"\n🧪 Test {i}: {test_case['task'][:60]}...")
        
        try:
            # Test task classification
            classified_domain = orchestrator.classify_task(test_case["task"])
            print(f"Classified domain: {classified_domain}")
            print(f"Expected domain: {test_case['expected_domain']}")
            
            if classified_domain == test_case["expected_domain"]:
                print("✅ Task classification correct")
            else:
                print("⚠️ Task classification differs from expected")
            
            # Run the task (commented out to avoid API calls during setup)
            # result = await orchestrator.run_task(test_case["task"])
            # print(f"Result: {result.final_output[:100]}...")
            
        except Exception as e:
            print(f"❌ Error running test {i}: {e}")
    
    return True


async def interactive_mode():
    """Run interactive mode for testing agents."""
    print("\n🎯 Interactive Multi-Agent System")
    print("=" * 50)
    print("Available commands:")
    print("  - Type a task to run it with auto-routing")
    print("  - 'healthcare: <task>' to route to healthcare agent")
    print("  - 'ai_research: <task>' to route to AI research agent")
    print("  - 'admin: <task>' to route to admin agent")
    print("  - 'memory' to test memory operations")
    print("  - 'quit' to exit")
    print("=" * 50)
    
    orchestrator = MultiAgentOrchestrator()
    
    while True:
        try:
            user_input = input("\n💬 Enter task: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            
            if user_input.lower() == 'memory':
                await test_memory_operations()
                continue
            
            if not user_input:
                continue
            
            # Parse domain-specific routing
            domain = None
            task = user_input
            
            if ':' in user_input:
                parts = user_input.split(':', 1)
                if len(parts) == 2:
                    potential_domain = parts[0].strip().lower()
                    if potential_domain in ['healthcare', 'ai_research', 'admin']:
                        domain = potential_domain
                        task = parts[1].strip()
            
            print(f"\n🚀 Processing task...")
            
            # For demo purposes, just show classification and routing
            if not domain:
                domain = orchestrator.classify_task(task)
            
            agent = orchestrator.get_agent_by_domain(domain)
            print(f"📍 Routed to: {agent.name} ({domain})")
            print(f"📝 Task: {task}")
            
            # Uncomment to actually run the agent (requires API setup)
            # result = await orchestrator.run_task(task, domain)
            # print(f"✅ Result: {result.final_output}")
            
            print("✅ Task routing successful (actual execution disabled for demo)")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n👋 Goodbye!")


async def main():
    """Main function."""
    print("🚀 Multi-Agent System with Mem0 MCP Integration")
    print("=" * 60)
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "memory":
            success = await test_memory_operations()
            sys.exit(0 if success else 1)
        
        elif command == "agents":
            success = await test_agents()
            sys.exit(0 if success else 1)
        
        elif command == "interactive":
            await interactive_mode()
        
        else:
            print(f"Unknown command: {command}")
            print("Available commands: memory, agents, interactive")
            sys.exit(1)
    
    else:
        # Default: run all tests
        print("Running all tests...")
        
        # Test agents
        agents_success = await test_agents()
        
        # Test memory operations
        memory_success = await test_memory_operations()
        
        print("\n" + "=" * 60)
        print("🏁 Final Results")
        print(f"Agents test: {'✅ PASS' if agents_success else '❌ FAIL'}")
        print(f"Memory test: {'✅ PASS' if memory_success else '❌ FAIL'}")
        
        if agents_success and memory_success:
            print("\n🎉 All systems operational!")
            print("\nTo run interactive mode: python main.py interactive")
        else:
            print("\n⚠️ Some tests failed. Check configuration and server connectivity.")


if __name__ == "__main__":
    asyncio.run(main())
