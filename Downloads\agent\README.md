# Multi-Agent System with Mem0 MCP Integration

A comprehensive multi-agent system built with the OpenAI SDK Agents framework, featuring three specialized domain agents and Mem0 MCP integration for memory operations.

## 🏗️ Architecture

### Specialized Agents

1. **Healthcare Agent** - Medical domain specialist
   - Medical terminology and clinical practices
   - Healthcare regulations (HIPAA, FDA)
   - Medical research and evidence-based medicine
   - Healthcare technology and digital health

2. **AI Research Agent** - AI/ML domain specialist
   - Machine Learning algorithms and architectures
   - Deep Learning and Neural Networks
   - Natural Language Processing and Computer Vision
   - AI model training, evaluation, and optimization

3. **Administrative Agent** - Administrative tasks specialist
   - Project management and task coordination
   - Documentation and report generation
   - Meeting scheduling and workflow optimization
   - Compliance monitoring and process management

### Key Features

- **Agent Handoffs**: Seamless delegation between specialized agents
- **Intelligent Routing**: Automatic task classification and agent selection
- **MCP Integration**: Mem0 memory operations for persistent context
- **LiteLLM Support**: Gemini API integration through LiteLLM
- **Comprehensive Testing**: Full test suite for all components

## 🚀 Quick Start

### Prerequisites

- Python 3.9+
- `uv` package manager
- Access to Gemini API
- Mem0 MCP server running (optional for memory features)

### Installation

1. **Set up the environment with uv:**
```bash
# Install uv if not already installed
curl -LsSf https://astral.sh/uv/install.sh | sh

# Create and activate virtual environment
cd Downloads/agent
uv venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
uv pip install -e .
```

2. **Configure environment variables (optional):**
```bash
# Create .env file
echo "GEMINI_API_KEY=AIzaSyBZWuoZTnFC5oNjbEV9BaBnodtEt3C7_Ns" > .env
echo "MEM0_MCP_URL=http://localhost:8765/mcp/openmemory/sse/Usama_Fresh" >> .env
```

## 🎯 Usage

### Run All Tests
```bash
python main.py
```

### Test Specific Components
```bash
# Test memory operations only
python main.py memory

# Test agents only
python main.py agents

# Interactive mode
python main.py interactive
```

### Interactive Mode Commands
- Type any task for automatic agent routing
- `healthcare: <task>` - Route to healthcare agent
- `ai_research: <task>` - Route to AI research agent  
- `admin: <task>` - Route to admin agent
- `memory` - Test memory operations
- `quit` - Exit

### Example Tasks

**Healthcare:**
```
Explain HIPAA compliance requirements for patient data
```

**AI Research:**
```
Compare transformer vs CNN architectures for image classification
```

**Administrative:**
```
Create a project timeline for software implementation
```

### Quick Demo
```bash
# Run interactive demonstration
python demo.py
```

## 🧠 Memory Operations

The system integrates with Mem0 MCP server for persistent memory:

- `add_memory()` - Add new memories
- `retrieve_memory()` - Search and fetch memories
- `store_memory()` - Update existing memories
- `delete_memory()` - Remove memories

### Memory Testing
```bash
python main.py memory
```

## 🧪 Testing

### Run Tests with pytest
```bash
# Install test dependencies
uv pip install pytest pytest-asyncio

# Run all tests
pytest tests/

# Run specific test file
pytest tests/test_memory_operations.py -v
```

### Test Coverage
- Agent creation and configuration
- Task classification and routing
- Agent handoffs between domains
- MCP server connectivity
- Memory operations (add, retrieve, store, delete)
- Error handling and edge cases

## 📁 Project Structure

```
Downloads/agent/
├── pyproject.toml              # Project configuration
├── main.py                     # Main entry point
├── demo.py                     # Interactive demonstration
├── README.md                   # Documentation
├── config/                     # Configuration management
│   ├── __init__.py
│   └── settings.py            # Settings and API configuration
├── domain_agents/              # Specialized agents
│   ├── __init__.py
│   ├── healthcare_agent.py    # Healthcare domain specialist
│   ├── ai_research_agent.py   # AI research domain specialist
│   ├── admin_agent.py         # Administrative specialist
│   └── orchestrator.py        # Multi-agent orchestration
├── memory_ops/                 # MCP integration
│   ├── __init__.py
│   ├── mem0_client.py         # Mem0 MCP client
│   └── memory_operations.py   # Memory testing utilities
└── tests/                      # Test suite
    ├── __init__.py
    └── test_memory_operations.py
```

## ⚙️ Configuration

### API Configuration
- **Gemini API Key**: `AIzaSyBZWuoZTnFC5oNjbEV9BaBnodtEt3C7_Ns`
- **Model**: `gemini-1.5-flash`
- **MCP Server**: `http://localhost:8765/mcp/openmemory/sse/Usama_Fresh`

### Customization
- Modify agent instructions in respective agent files
- Update API settings in `config/settings.py`
- Add domain-specific tools to agent modules
- Extend memory operations in `memory_ops/memory_operations.py`

## 🔧 Troubleshooting

### Common Issues

1. **MCP Connection Failed**
   - Ensure Mem0 MCP server is running
   - Check server URL in configuration
   - Verify network connectivity

2. **Agent API Errors**
   - Verify Gemini API key is valid
   - Check API rate limits
   - Ensure LiteLLM is properly configured

3. **Import Errors**
   - Activate virtual environment
   - Install dependencies with `uv pip install -e .`
   - Check Python version (3.9+ required)

### Debug Mode
Set environment variable for detailed logging:
```bash
export DEBUG=1
python main.py
```

## 🤝 Contributing

1. Follow OpenAI SDK Agents framework patterns
2. Maintain clean, readable code structure
3. Add tests for new functionality
4. Update documentation for changes

## 📄 License

MIT License - see project configuration for details.
