"""Multi-agent orchestration logic."""

from typing import Dict, Any, List, Optional
from agents import Agent, Runner, Handoff

from domain_agents.healthcare_agent import create_healthcare_agent
from domain_agents.ai_research_agent import create_ai_research_agent
from domain_agents.admin_agent import create_admin_agent


class MultiAgentOrchestrator:
    """Orchestrates multiple specialized agents."""
    
    def __init__(self):
        """Initialize the orchestrator with all agents."""
        self.healthcare_agent = create_healthcare_agent()
        self.ai_research_agent = create_ai_research_agent()
        self.admin_agent = create_admin_agent()
        
        # Set up handoffs between agents
        self._setup_handoffs()
    
    def _setup_handoffs(self):
        """Set up handoff relationships between agents."""
        
        # Healthcare agent handoffs
        healthcare_handoffs = [
            Handoff(
                target=self.ai_research_agent,
                description="Hand off to AI Research Agent for AI/ML related healthcare questions"
            ),
            Handoff(
                target=self.admin_agent,
                description="Hand off to Admin Agent for administrative healthcare tasks"
            )
        ]
        
        # AI Research agent handoffs
        ai_research_handoffs = [
            Handoff(
                target=self.healthcare_agent,
                description="Hand off to Healthcare Agent for medical domain AI applications"
            ),
            Handoff(
                target=self.admin_agent,
                description="Hand off to Admin Agent for research project administration"
            )
        ]
        
        # Admin agent handoffs
        admin_handoffs = [
            Handoff(
                target=self.healthcare_agent,
                description="Hand off to Healthcare Agent for healthcare-related administrative tasks"
            ),
            Handoff(
                target=self.ai_research_agent,
                description="Hand off to AI Research Agent for AI research administration"
            )
        ]
        
        # Update agents with handoffs
        self.healthcare_agent.handoffs = healthcare_handoffs
        self.ai_research_agent.handoffs = ai_research_handoffs
        self.admin_agent.handoffs = admin_handoffs
    
    def get_agent_by_domain(self, domain: str) -> Optional[Agent]:
        """Get agent by domain name."""
        domain_map = {
            "healthcare": self.healthcare_agent,
            "ai_research": self.ai_research_agent,
            "admin": self.admin_agent,
        }
        return domain_map.get(domain.lower())
    
    def classify_task(self, task: str) -> str:
        """Classify task to determine which agent should handle it."""
        task_lower = task.lower()
        
        # Healthcare keywords
        healthcare_keywords = [
            "medical", "health", "patient", "clinical", "diagnosis", "treatment",
            "drug", "medication", "hospital", "doctor", "nurse", "healthcare",
            "disease", "symptom", "therapy", "pharmaceutical", "hipaa", "fda"
        ]
        
        # AI Research keywords
        ai_keywords = [
            "machine learning", "deep learning", "neural network", "ai", "model",
            "algorithm", "training", "dataset", "research", "paper", "experiment",
            "pytorch", "tensorflow", "transformer", "llm", "nlp", "computer vision"
        ]
        
        # Admin keywords
        admin_keywords = [
            "schedule", "meeting", "document", "report", "project", "manage",
            "organize", "calendar", "email", "budget", "compliance", "audit",
            "workflow", "process", "coordination", "planning"
        ]
        
        # Count keyword matches
        healthcare_score = sum(1 for keyword in healthcare_keywords if keyword in task_lower)
        ai_score = sum(1 for keyword in ai_keywords if keyword in task_lower)
        admin_score = sum(1 for keyword in admin_keywords if keyword in task_lower)
        
        # Determine best match
        scores = {
            "healthcare": healthcare_score,
            "ai_research": ai_score,
            "admin": admin_score
        }
        
        best_domain = max(scores, key=scores.get)
        
        # If no clear match, default to admin for general tasks
        if scores[best_domain] == 0:
            return "admin"
        
        return best_domain
    
    async def run_task(self, task: str, domain: Optional[str] = None) -> Any:
        """Run a task with the appropriate agent."""
        
        # Determine which agent to use
        if domain:
            agent = self.get_agent_by_domain(domain)
            if not agent:
                raise ValueError(f"Unknown domain: {domain}")
        else:
            domain = self.classify_task(task)
            agent = self.get_agent_by_domain(domain)
        
        print(f"Routing task to {domain} agent: {agent.name}")
        
        # Run the task with the selected agent
        result = await Runner.run(agent, task)
        return result
    
    def get_available_agents(self) -> Dict[str, Agent]:
        """Get all available agents."""
        return {
            "healthcare": self.healthcare_agent,
            "ai_research": self.ai_research_agent,
            "admin": self.admin_agent,
        }
