"""Memory operations testing and utilities."""

import async<PERSON>
from typing import Dict, Any, List
from .mem0_client import Mem0MCPClient


class MemoryOperationsTester:
    """Test suite for Mem0 memory operations."""
    
    def __init__(self, client: Mem0MCPClient):
        """Initialize with Mem0 client."""
        self.client = client
        self.test_results = []
    
    async def test_add_memory(self) -> Dict[str, Any]:
        """Test adding a new memory."""
        print("\n=== Testing add_memory ===")
        
        test_content = "This is a test memory for the multi-agent system"
        test_metadata = {
            "source": "test_suite",
            "category": "system_test",
            "timestamp": "2024-01-01T00:00:00Z"
        }
        
        try:
            result = await self.client.add_memory(test_content, test_metadata)
            
            test_result = {
                "operation": "add_memory",
                "success": "error" not in result,
                "result": result,
                "test_data": {
                    "content": test_content,
                    "metadata": test_metadata
                }
            }
            
            self.test_results.append(test_result)
            
            if test_result["success"]:
                print(f"✅ Successfully added memory: {result}")
            else:
                print(f"❌ Failed to add memory: {result}")
            
            return test_result
            
        except Exception as e:
            error_result = {
                "operation": "add_memory",
                "success": False,
                "error": str(e),
                "test_data": {
                    "content": test_content,
                    "metadata": test_metadata
                }
            }
            self.test_results.append(error_result)
            print(f"❌ Exception in add_memory: {e}")
            return error_result
    
    async def test_retrieve_memory(self) -> Dict[str, Any]:
        """Test retrieving memories."""
        print("\n=== Testing retrieve_memory ===")
        
        test_query = "test memory multi-agent"
        
        try:
            result = await self.client.retrieve_memory(test_query, limit=5)
            
            test_result = {
                "operation": "retrieve_memory",
                "success": "error" not in result,
                "result": result,
                "test_data": {
                    "query": test_query,
                    "limit": 5
                }
            }
            
            self.test_results.append(test_result)
            
            if test_result["success"]:
                print(f"✅ Successfully retrieved memories: {result}")
            else:
                print(f"❌ Failed to retrieve memories: {result}")
            
            return test_result
            
        except Exception as e:
            error_result = {
                "operation": "retrieve_memory",
                "success": False,
                "error": str(e),
                "test_data": {
                    "query": test_query,
                    "limit": 5
                }
            }
            self.test_results.append(error_result)
            print(f"❌ Exception in retrieve_memory: {e}")
            return error_result
    
    async def test_store_memory(self) -> Dict[str, Any]:
        """Test storing/updating a memory."""
        print("\n=== Testing store_memory ===")
        
        test_memory_id = "test_memory_001"
        test_content = "Updated test memory content for multi-agent system"
        test_metadata = {
            "source": "test_suite",
            "category": "system_test_updated",
            "timestamp": "2024-01-01T01:00:00Z"
        }
        
        try:
            result = await self.client.store_memory(test_memory_id, test_content, test_metadata)
            
            test_result = {
                "operation": "store_memory",
                "success": "error" not in result,
                "result": result,
                "test_data": {
                    "memory_id": test_memory_id,
                    "content": test_content,
                    "metadata": test_metadata
                }
            }
            
            self.test_results.append(test_result)
            
            if test_result["success"]:
                print(f"✅ Successfully stored memory: {result}")
            else:
                print(f"❌ Failed to store memory: {result}")
            
            return test_result
            
        except Exception as e:
            error_result = {
                "operation": "store_memory",
                "success": False,
                "error": str(e),
                "test_data": {
                    "memory_id": test_memory_id,
                    "content": test_content,
                    "metadata": test_metadata
                }
            }
            self.test_results.append(error_result)
            print(f"❌ Exception in store_memory: {e}")
            return error_result
    
    async def test_delete_memory(self) -> Dict[str, Any]:
        """Test deleting a memory."""
        print("\n=== Testing delete_memory ===")
        
        test_memory_id = "test_memory_001"
        
        try:
            result = await self.client.delete_memory(test_memory_id)
            
            test_result = {
                "operation": "delete_memory",
                "success": "error" not in result,
                "result": result,
                "test_data": {
                    "memory_id": test_memory_id
                }
            }
            
            self.test_results.append(test_result)
            
            if test_result["success"]:
                print(f"✅ Successfully deleted memory: {result}")
            else:
                print(f"❌ Failed to delete memory: {result}")
            
            return test_result
            
        except Exception as e:
            error_result = {
                "operation": "delete_memory",
                "success": False,
                "error": str(e),
                "test_data": {
                    "memory_id": test_memory_id
                }
            }
            self.test_results.append(error_result)
            print(f"❌ Exception in delete_memory: {e}")
            return error_result
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all memory operation tests."""
        print("🧪 Starting Mem0 Memory Operations Test Suite")
        print("=" * 50)
        
        # Test connection first
        if not await self.client.is_connected():
            print("❌ Not connected to Mem0 MCP server")
            return {"success": False, "error": "Not connected to server"}
        
        # Run all tests
        await self.test_add_memory()
        await self.test_retrieve_memory()
        await self.test_store_memory()
        await self.test_delete_memory()
        
        # Generate summary
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result["success"])
        
        summary = {
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "failed_tests": total_tests - successful_tests,
            "success_rate": successful_tests / total_tests if total_tests > 0 else 0,
            "test_results": self.test_results
        }
        
        print("\n" + "=" * 50)
        print("🧪 Test Suite Summary")
        print(f"Total tests: {total_tests}")
        print(f"Successful: {successful_tests}")
        print(f"Failed: {total_tests - successful_tests}")
        print(f"Success rate: {summary['success_rate']:.2%}")
        
        return summary
